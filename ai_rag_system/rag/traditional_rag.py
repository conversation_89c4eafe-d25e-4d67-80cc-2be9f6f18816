import asyncio
import os
from datetime import datetime
from filetype import is_image
from llama_index.core import SimpleDire<PERSON><PERSON><PERSON><PERSON><PERSON>, Document
from ai_rag_system.rag.utils import ocr_file_to_text_llm
from ai_rag_system.rag.base_rag import RAG
from ai_rag_system.rag.ocr import ocr_file_to_text, orc_image_to_text

class TraditionalRAG(RAG):
    async def load_data(self):
        docs = []
        for file in self.files:
            # 对图片及文档通过Moonshot大模型进行OCR识别
            # contents = ocr_file_to_text_llm(file)
            # 判断文件类型
            if is_image(file):
                contents = orc_image_to_text(file)
                temp_file = datetime.now().strftime("%Y%m%d%H%M%S") + ".txt"
                with open(temp_file, "w", encoding="utf-8") as f:
                    f.write(contents)
                    f_name = temp_file
            else:
                f_name = ocr_file_to_text(file)
            # temp_file = datetime.now().strftime("%Y%m%d%H%M%S") + ".txt"
            # with open(temp_file, "w", encoding="utf-8") as f:
            #     f.write(contents)

            data = SimpleDirectoryReader(input_files=[f_name]).load_data()
            doc = Document(text="\n\n".join([d.text for d in data[0:]]), metadata={"path": file})
            docs.append(doc)
            os.remove(f_name)
        return docs


if __name__ == "__main__":
    rag = TraditionalRAG(files=["请假流程.png"])
    asyncio.run(rag.create_index_local(persist_dir="data"))
