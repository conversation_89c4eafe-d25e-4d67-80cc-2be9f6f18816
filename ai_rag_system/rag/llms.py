from typing import Dict

from llama_index.llms.openai import OpenAI as llama_index_OpenAI
from openai import OpenAI
from llama_index.llms.openai.utils import ALL_AVAILABLE_MODELS, CHAT_MODELS

from ai_rag_system.rag.config import RagConfig


GITEE_MODELS: Dict[str, int] = {
    "DeepSeek-V3": 200000,
    "kimi-k2-instruct": 200000,

}
ALL_AVAILABLE_MODELS.update(GITEE_MODELS)
CHAT_MODELS.update(GITEE_MODELS)

FASTGPT_MODELS: Dict[str, int] = {
    "claude-3-5-haiku-20241022": 200000,

}
ALL_AVAILABLE_MODELS.update(FASTGPT_MODELS)
CHAT_MODELS.update(FASTGPT_MODELS)

def gitee_deepseek_v3_llm(**kwargs):
    llm = llama_index_OpenAI(api_key=RagConfig.deepseek_api_key,
                     model="DeepSeek-V3",
                     api_base="https://ai.gitee.com/v1",
                     **kwargs)
    return llm

def gitee_kimi_k2_llm(**kwargs):
    llm = llama_index_OpenAI(api_key=RagConfig.kimi_api_key,
                     model="kimi-k2-instruct",
                      api_base="https://ai.gitee.com/v1",
                     **kwargs)
    return llm

def fastGPT_claude_llm(**kwargs):
    llm = llama_index_OpenAI(api_key=RagConfig.fastgpt_api_key,
                     model="claude-3-5-haiku-20241022",
                      api_base="https://api.fastgpt.in/api/v1",
                     **kwargs)
    return llm

def moonshot_llm(**kwargs):
    llm = OpenAI(api_key=RagConfig.moonshot_api_key,
                      base_url="https://api.moonshot.cn/v1",
                     **kwargs)
    return llm
