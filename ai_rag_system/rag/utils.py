from ai_rag_system.rag.llms import moonshot_llm
from pathlib import Path
import base64
def ocr_file_to_text_llm(file_path) -> str:
    """
    识别文件中的文字
    """
    client = moonshot_llm()
    file_object = client.files.create(file=Path(file_path), purpose="file-extract")
    file_content = client.files.content(file_id=file_object.id).json()
    return file_content.get("content")

def ocr_file_to_text_local(file_path) -> str:
    pass

def get_b64_image_from_path(image_path):
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')