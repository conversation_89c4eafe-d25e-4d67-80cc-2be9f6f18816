# Chainlit 翻译配置说明

## 翻译文件结构

### zh-CN.json 文件结构说明

这个文件包含了 Chainlit 应用的所有中文翻译内容，按功能模块组织：

#### 1. common - 通用组件
```json
"common": {
    "actions": {
        "cancel": "取消",        // 取消按钮
        "confirm": "确认",       // 确认按钮
        "continue": "继续",      // 继续按钮
        "goBack": "返回",        // 返回按钮
        "reset": "重置",         // 重置按钮
        "submit": "提交"         // 提交按钮
    },
    "status": {
        "loading": "加载中...",  // 加载状态提示
        "error": {
            "default": "发生错误",                    // 默认错误信息
            "serverConnection": "无法连接到服务器"     // 服务器连接错误
        }
    }
}
```

#### 2. auth - 用户认证
```json
"auth": {
    "login": {
        "title": "登录以访问应用",    // 登录页面标题
        "form": {
            "email": {
                "label": "电子邮箱",           // 邮箱输入框标签
                "required": "邮箱是必填项"     // 邮箱必填提示
            },
            "password": {
                "label": "密码",               // 密码输入框标签
                "required": "密码是必填项"     // 密码必填提示
            }
        }
    }
}
```

#### 3. chat - 聊天功能
```json
"chat": {
    "input": {
        "placeholder": "在此输入您的消息...",  // 输入框占位符
        "actions": {
            "send": "发送消息",              // 发送按钮
            "stop": "停止任务",              // 停止按钮
            "attachFiles": "附加文件"        // 附件按钮
        }
    },
    "fileUpload": {
        "dragDrop": "将文件拖放到这里",       // 拖拽上传提示
        "browse": "浏览文件",               // 浏览文件按钮
        "sizeLimit": "限制：",              // 文件大小限制提示
        "errors": {
            "failed": "上传失败",           // 上传失败提示
            "cancelled": "已取消上传"       // 取消上传提示
        }
    }
}
```

#### 4. threadHistory - 对话历史
```json
"threadHistory": {
    "sidebar": {
        "title": "历史对话",                // 侧边栏标题
        "filters": {
            "search": "搜索",               // 搜索功能
            "placeholder": "搜索会话..."    // 搜索框占位符
        },
        "timeframes": {
            "today": "今天",                // 时间筛选：今天
            "yesterday": "昨天",            // 时间筛选：昨天
            "previous7days": "过去7天",     // 时间筛选：过去7天
            "previous30days": "过去30天"    // 时间筛选：过去30天
        }
    }
}
```

#### 5. navigation - 导航栏
```json
"navigation": {
    "header": {
        "chat": "聊天",                     // 聊天标签
        "readme": "说明",                   // 说明文档标签
        "theme": {
            "light": "浅色主题",            // 浅色主题选项
            "dark": "深色主题",             // 深色主题选项
            "system": "跟随系统"            // 跟随系统主题选项
        }
    },
    "newChat": {
        "button": "新建对话",               // 新建对话按钮
        "dialog": {
            "title": "创建新对话",          // 新建对话弹窗标题
            "description": "创建新的聊天。确定要继续吗？",  // 确认提示
            "tooltip": "新建对话"           // 按钮提示文本
        }
    }
}
```

## 自定义翻译指南

### 1. 修改现有翻译
要修改某个文本的翻译，找到对应的键值对并修改其值：

```json
// 例如修改新建对话的确认提示
"description": "创建新的聊天。确定要继续吗？"
```

### 2. 添加新的翻译项
如果需要添加新的翻译项，按照现有的层级结构添加：

```json
"navigation": {
    "newChat": {
        "dialog": {
            "customMessage": "您的自定义消息"  // 新增的翻译项
        }
    }
}
```

### 3. 常用自定义场景

#### 修改应用标题和描述
在 config.toml 中修改：
```toml
[UI]
name = "您的应用名称"
description = "您的应用描述"
```

#### 修改按钮文本
在 zh-CN.json 中修改：
```json
"common": {
    "actions": {
        "submit": "发送"  // 将"提交"改为"发送"
    }
}
```

#### 修改错误提示信息
```json
"common": {
    "status": {
        "error": {
            "default": "系统繁忙，请稍后重试"  // 自定义错误信息
        }
    }
}
```

## 注意事项

1. **文件编码**：确保文件使用 UTF-8 编码保存
2. **JSON 格式**：保持正确的 JSON 语法，注意逗号和引号
3. **层级结构**：保持与原文件相同的层级结构
4. **重启应用**：修改翻译文件后需要重启 Chainlit 应用才能生效
5. **浏览器缓存**：如果修改未生效，尝试清除浏览器缓存或硬刷新（Ctrl+F5）

## 优先级说明

翻译文件的加载优先级：
1. **项目本地翻译文件**（`ai_rag_system/.chainlit/translations/zh-CN.json`）- 最高优先级
2. 全局配置翻译文件
3. Chainlit 包默认翻译文件 - 最低优先级

建议始终修改项目本地的翻译文件，这样不会因为包升级而丢失自定义内容。
