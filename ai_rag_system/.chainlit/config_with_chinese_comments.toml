# Chainlit 配置文件 - 中文注释版本
# 这个文件包含了 Chainlit 应用的所有配置选项和详细的中文说明

[project]
# 项目基础配置
# 每个用户使用应用时需要提供的环境变量列表
user_env = []

# 连接丢失时会话保存的持续时间（秒）
session_timeout = 3600

# 用户会话过期的持续时间（秒）
user_session_timeout = 1296000  # 15天

# 启用第三方缓存（例如 LangChain 缓存）
cache = false

# 授权的来源域名列表，["*"] 表示允许所有来源
allow_origins = ["*"]

[features]
# 功能特性配置

# 在消息中处理和显示HTML内容
# 警告：这可能存在安全风险，因为可能执行恶意脚本
unsafe_allow_html = false

# 处理和显示数学表达式（LaTeX格式）
# 注意：这可能与消息中的"$"字符冲突
latex = false

# 自动滚动新用户消息到窗口顶部
user_message_autoscroll = true

# 自动为线程标记当前聊天配置文件（如果使用了聊天配置文件）
auto_tag_thread = true

# 允许用户编辑自己的消息
edit_message = true

# 文件上传功能配置
[features.spontaneous_file_upload]
    # 启用用户随消息上传文件的功能
    enabled = true
    
    # 定义接受的文件类型（使用MIME类型）
    # 示例配置：
    # 1. 特定文件类型：
    #    accept = ["image/jpeg", "image/png", "application/pdf"]
    # 2. 某种类型的所有文件：
    #    accept = ["image/*", "audio/*", "video/*"]
    # 3. 特定文件扩展名：
    #    accept = { "application/octet-stream" = [".xyz", ".pdb"] }
    # 注意：使用"*/*"不推荐，可能导致浏览器安全警告
    accept = ["*/*"]
    
    # 最大文件数量限制
    max_files = 20
    
    # 单个文件最大大小限制（MB）
    max_size_mb = 500

# 音频功能配置
[features.audio]
    # 音频采样率设置
    sample_rate = 24000

# MCP (Model Context Protocol) 功能配置
[features.mcp.sse]
    # 启用 Server-Sent Events 支持
    enabled = true

[features.mcp.stdio]
    # 启用标准输入输出支持
    enabled = true
    
    # 允许用于MCP stdio服务器的可执行文件白名单
    # 只需要可执行文件的基本名称，例如"npx"而不是"/usr/bin/npx"
    allowed_executables = [ "npx", "uvx" ]

# 用户界面配置
[UI]
# 应用名称，显示在浏览器标题栏
name = "AI RAG System"

# 应用描述
description = "基于大语言模型的智能问答系统"

# 是否隐藏 README 页面
hide_readme = false

# 默认折叠代码块
default_collapse_content = true

# 默认展开消息操作按钮
default_expand_messages = false

# GitHub 仓库链接（可选）
github = ""

# 自定义主题配置
[UI.theme]
    # 默认主题模式："light"（浅色）、"dark"（深色）或"system"（跟随系统）
    default = "system"
    
    # 自定义CSS文件路径（相对于项目根目录）
    # custom_css = "./public/custom.css"

# 头像配置
# 默认用户头像文件路径
default_user_avatar = ""

# 默认助手头像文件路径  
default_assistant_avatar = ""

# 从URL直接加载助手头像图片
default_avatar_file_url = ""

# 自定义前端构建目录
# 可用于自定义前端代码
# 注意：如果是相对路径，不应以斜杠开头
# custom_build = "./public/build"

# 头部链接配置（可选）
# [[UI.header_links]]
#     name = "Issues"
#     display_name = "报告问题"
#     icon_url = "https://avatars.githubusercontent.com/u/128686189?s=200&v=4"
#     url = "https://github.com/Chainlit/chainlit/issues"

[meta]
# 元数据：生成此配置文件的Chainlit版本
generated_by = "2.6.3"
